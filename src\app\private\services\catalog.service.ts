import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BasicService } from '../../service/basic.service';
import { 
  SectorHierarchyResponse, 
  TypologyContent, 
  DatacolCategoryTagsResponse,
  AddRemoveTagRequest,
  AddRemoveTagResponse
} from '../model/sector-hierarchy-response';

@Injectable({
  providedIn: 'root'
})
export class CatalogsService extends BasicService {

  constructor() {
    super();
  }

  /**
   * Ottiene la gerarchia dei settori per un catalogo
   * @param params Parametri contenenti idCatalog
   * @returns Observable<TypologyContent>
   */
  getSectorHierarchy(params: { idCatalog: number }): Observable<TypologyContent> {
    this.logAuthenticationStatus('getSectorHierarchy');
    const url = `/appcatalogs/getSectorHierarchy?idCatalog=${params.idCatalog}`;
    console.log('🔗 CatalogsService.getSectorHierarchy - URL:', url);
    
    return new Observable(observer => {
      this.basicGet(url, false).then((response: SectorHierarchyResponse) => {
        if (response.status === 'OK') {
          observer.next(response.content);
        } else {
          observer.error(response.error || 'Error loading sector hierarchy');
        }
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Ottiene i tag assegnati a una categoria
   * @param params Parametri contenenti idSubCategory e idCatalog
   * @returns Observable<DatacolCategoryTagsResponse>
   */
  getDatacolCategoryTags(params: { idSubCategory: number; idCatalog: number }): Observable<DatacolCategoryTagsResponse> {
    this.logAuthenticationStatus('getDatacolCategoryTags');
    const url = `/appcatalogs/getDatacolCategoryTags?idSubCategory=${params.idSubCategory}&idCatalog=${params.idCatalog}`;
    console.log('🔗 CatalogsService.getDatacolCategoryTags - URL:', url);
    
    return new Observable(observer => {
      this.basicGet(url, false).then((response: DatacolCategoryTagsResponse) => {
        observer.next(response);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Aggiunge un tag a una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  addDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    this.logAuthenticationStatus('addDatacolCategoryTag');
    const url = `/appcatalogs/addDatacolCategoryTag`;
    console.log('🔗 CatalogsService.addDatacolCategoryTag - URL:', url, 'Request:', request);
    
    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: AddRemoveTagResponse) => {
        observer.next(response);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Rimuove un tag da una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  removeDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    this.logAuthenticationStatus('removeDatacolCategoryTag');
    const url = `/appcatalogs/removeDatacolCategoryTag`;
    console.log('🔗 CatalogsService.removeDatacolCategoryTag - URL:', url, 'Request:', request);
    
    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: AddRemoveTagResponse) => {
        observer.next(response);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }
}
